# Tee 代理商订单处理配置

## 默认预设字段

以下字段已有默认预设值，Gemini AI 在处理订单时只需要返回除这些字段以外的内容：

### 预设字段列表
- **ota**: "Tee"
- **ota_reference_number**: 自动生成（格式：TEE-YYYYMMDD-XXX）
- **ota_price**: 0
- **customer_contact**: 自动生成（格式：TEE-YYYYMMDD-HHMM-[姓名]）
- **customer_email**: "<EMAIL>"
- **passenger_number**: 1
- **luggage_number**: 1
- **driver_fee**: 1
- **extra_requirement**: "Tee代理商订单，请按照标准流程处理"

## Tee 代理商特殊规则

### 1. 订单格式识别
- 识别"OTA: Tee"或"OTA：Tee"标识
- 处理相对简单的订单格式
- 注意车型要求（如Toyota Wish、Honda MPV等）

### 2. 日期时间处理
- 确保所有日期为未来日期
- 时间格式标准化为24小时制
- 基于当前日期进行日期修正

### 3. 车型信息
- 识别并保留车型要求
- 常见车型：Toyota Wish, Honda MPV, Toyota Alphard等
- 如有特殊车型要求，记录在other字段

### 4. 联系信息生成
- 电话格式：TEE-YYYYMMDD-HHMM-[姓名]
- OTA参考号：TEE-YYYYMMDD-XXX
- 邮箱：统一使用 <EMAIL>

### 5. 本地识别特征

系统会通过以下关键词进行本地识别：
- "tee"
- "TEE"
- "代理商"
- "agent"
- "toyota wish"
- "honda mpv"
- "车型"

## 最终结果输出格式

Gemini AI 只需要返回以下字段（不要返回默认预设字段）：

```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息，包括车型要求等]
```

## 注意事项

1. **不要返回预设字段**：系统会自动应用上述预设值
2. **车型信息**：如有特殊车型要求，请记录在other字段中
3. **日期修正**：确保所有日期都是未来日期
4. **格式统一**：严格按照指定格式输出结果