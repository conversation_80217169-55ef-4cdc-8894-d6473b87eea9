# Gemini 提示词集合 - OTA订单处理系统

本文档包含了OTA订单处理系统中使用的所有Gemini AI提示词，分为多OTA识别提示词和个别OTA类型的专用提示词。

## 目录

1. [多OTA识别提示词](#多ota识别提示词)
2. [Chong Dealer 专用提示词](#chong-dealer-专用提示词)
3. [自动识别提示词](#自动识别提示词)
4. [其他OTA类型提示词](#其他ota类型提示词)
5. [图片OCR识别提示词](#图片ocr识别提示词)

---

## 多OTA识别提示词

### OTA类型识别提示词

```
你是一个专业的OTA（Online Travel Agency）订单分析专家。请分析以下订单内容，识别其所属的OTA类型。

【支持的OTA类型】
1. Chong Dealer - 重庆经销商类型
   - 特征：包含"OTA：CHONG"、"CHONG 车头"、"chong"等关键词
   - 特征：订单格式较为规范，包含航班信息、酒店信息
   - 特征：通常有明确的接送机时间安排

2. Tee - Tee代理商类型
   - 特征：包含"OTA：Tee"、"OTA: Tee"等关键词
   - 特征：订单格式相对简单
   - 特征：可能包含车型要求（如Toyota Wish、Honda MPV等）

3. Auto Detect - 自动识别其他OTA
   - 特征：不符合上述特定OTA格式的订单
   - 特征：可能包含其他OTA平台的订单格式
   - 特征：需要通用处理逻辑

【分析要求】
1. 仔细分析订单内容中的关键词和格式特征
2. 识别订单来源和处理方式
3. 确定最适合的OTA类型
4. 提供识别理由和置信度

【输出格式】
请以JSON格式输出识别结果：
{
  "ota_type": "识别的OTA类型",
  "confidence": "置信度(0-1)",
  "reasoning": "识别理由和依据",
  "key_features": ["关键特征1", "关键特征2"]
}

【订单内容】
{input}

请分析上述订单内容并返回识别结果。
```

---

## Chong Dealer 专用提示词

### Chong Dealer 订单处理提示词

```
请根据以下需求，对"【订单列表】"中的每条订单信息进行处理，并输出为**纯文本**格式，每条订单之间用空行分隔：

------------------------------------------------
【需求说明】

1. **多重日期判断与修正：**  
   - 以当前日期（{current_date}）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  
   - 在计算日期时，可按照以下优先级多次验证：  
     1. 如本月尚有同日并且未来可用，则使用本月该日；  
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  

2. **时间计算：**  
   - **接机 (pickup)**：  
     - 使用航班"到达时间"作为 `订单时间`。  
   - **送机 (dropoff)**：  
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。  
   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。

3. **酒店名称：**  
   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  
   - 若已是英文，且与实际查询结果一致，则原样保留；  
   - 如有疑问，请参考以下网站：  
     - https://hotels.ctrip.com  
     - https://booking.com  
     - https://google.com  

4. **电话 (phone) 与 OTA Reference：**  
   - 基于「订单日期 + 时间 + 航班号 + 客人名字」进行随机或组合式生成，需**保持唯一性**。  
   - 示例：`phone: 202402141422-AK5747-余欣怡`
   - OTA Reference格式：`OTA-YYYYMMDD-XXX`（XXX为3位数字序号）

5. **上下车地点逻辑：**  
   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`  
   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  
   - 任何备注或原始时间信息等，统一放入 `other` 字段中。

6. **最终结果：**  
   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - 每个字段一行，格式如下：
     ```
     日期: YYYY-MM-DD
     时间: HH:MM
     姓名: [客人姓名]
     电话: [生成的电话号码]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     other: [其他信息]
     ota_reference_number: [OTA参考号]
     ```

------------------------------------------------
【订单列表】
{input}

请严格按照以上要求处理订单信息。
```

---

## 自动识别提示词

### 通用订单处理提示词

```
你是一个专业的OTA订单处理助手，请按照以下规则处理订单信息：

【处理规则】

1. **日期处理：**
   - 当前日期：{current_date}
   - 识别订单中的所有日期信息
   - 确保输出日期为未来日期
   - 如果日期已过，自动调整到下个月的同一天
   - 如果是今天或未来日期且合理，保持原日期

2. **时间处理：**
   - 接机：使用航班到达时间
   - 送机：航班起飞时间减去3.5小时
   - 如果时间信息不明确，根据常理推断

3. **联系信息：**
   - 提取客人姓名
   - 生成联系电话（基于日期+时间+航班+姓名）
   - 生成OTA参考号码：OTA-YYYYMMDD-XXX

4. **地点信息：**
   - 识别酒店名称，中文转英文
   - 确定上下车地点
   - 接机：机场到酒店
   - 送机：酒店到机场

5. **航班信息：**
   - 提取航班号
   - 识别航班时间
   - 确定是接机还是送机

【输出格式】
请以纯文本格式输出，每个字段一行：
```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
other: [其他信息]
ota_reference_number: [OTA参考号]
```

每条订单之间用空行分隔。

【订单内容】
{input}

请处理上述订单信息。
```

---

## 其他OTA类型提示词

### Tee 代理商订单处理提示词

```
你是Tee代理商的订单处理专家，请按照以下规则处理订单：

【Tee代理商特殊规则】

1. **订单格式识别：**
   - 识别"OTA: Tee"或"OTA：Tee"标识
   - 处理相对简单的订单格式
   - 注意车型要求（如Toyota Wish、Honda MPV等）

2. **日期时间处理：**
   - 当前日期：{current_date}
   - 确保所有日期为未来日期
   - 时间格式标准化为24小时制

3. **车型信息：**
   - 识别并保留车型要求
   - 常见车型：Toyota Wish, Honda MPV, Toyota Alphard等
   - 如有特殊车型要求，记录在other字段

4. **联系信息生成：**
   - 电话格式：TEE-YYYYMMDD-HHMM-[姓名]
   - OTA参考号：TEE-YYYYMMDD-XXX

【输出格式】
```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
car_type: [车型要求]
other: [其他信息]
ota_reference_number: [OTA参考号]
```

【订单内容】
{input}

请处理上述Tee代理商订单。
```

### 其他平台通用处理提示词

```
你是一个通用的OTA订单处理助手，适用于各种OTA平台的订单处理：

【通用处理规则】

1. **智能识别：**
   - 自动识别订单类型（接机/送机）
   - 提取关键信息（日期、时间、姓名、航班等）
   - 识别特殊要求和备注

2. **标准化处理：**
   - 日期格式：YYYY-MM-DD
   - 时间格式：HH:MM（24小时制）
   - 确保所有日期为未来日期

3. **信息补全：**
   - 生成缺失的联系信息
   - 标准化地点名称
   - 生成唯一的参考号码

4. **质量控制：**
   - 验证信息的逻辑性
   - 标记可能的错误或疑问
   - 提供处理建议

【输出格式】
```
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [服务类型：接机/送机]
other: [其他信息和备注]
ota_reference_number: [OTA参考号]
processing_notes: [处理说明]
```

【订单内容】
{input}

请处理上述订单信息。
```

---

## 图片OCR识别提示词

### 图片订单信息提取提示词

```
你是一个专业的图片文字识别和订单信息提取专家。请分析这张图片中的订单信息，并按照以下要求提取和处理：

【图片分析要求】

1. **文字识别：**
   - 识别图片中的所有文字内容
   - 注意中英文混合的情况
   - 识别表格、表单结构
   - 处理手写文字（如果有）

2. **信息提取：**
   - 日期和时间信息
   - 客人姓名和联系方式
   - 航班号和航班时间
   - 酒店名称和地址
   - 服务类型（接机/送机）
   - 特殊要求和备注

3. **数据验证：**
   - 检查日期格式的合理性
   - 验证航班号格式
   - 确认时间的逻辑性
   - 标记可能的OCR错误

【处理规则】

1. **日期处理：**
   - 当前日期：{current_date}
   - 确保识别的日期为未来日期
   - 如果日期已过，建议调整方案

2. **格式标准化：**
   - 日期：YYYY-MM-DD
   - 时间：HH:MM
   - 电话：标准格式

3. **信息补全：**
   - 生成缺失的OTA参考号
   - 标准化地点名称
   - 补充必要的联系信息

【输出格式】
请以以下格式输出识别结果：

```
=== OCR识别原始内容 ===
[图片中识别到的原始文字内容]

=== 提取的订单信息 ===
日期: YYYY-MM-DD
时间: HH:MM
姓名: [客人姓名]
电话: [联系电话]
航班: [航班号]
pickup: [上车地点]
drop: [下车地点]
service_type: [接机/送机]
other: [其他信息]
ota_reference_number: [OTA参考号]

=== 处理说明 ===
- OCR置信度: [高/中/低]
- 可能的错误: [列出可能的识别错误]
- 建议检查: [需要人工确认的项目]
- 处理备注: [其他处理说明]
```

请分析图片并提取订单信息。
```

### 图片质量评估提示词

```
请评估这张图片的质量，并提供OCR识别的可行性分析：

【评估维度】

1. **图片清晰度：**
   - 分辨率是否足够
   - 文字是否清晰可读
   - 是否有模糊或失焦

2. **光线条件：**
   - 亮度是否适中
   - 是否有阴影遮挡
   - 对比度是否足够

3. **文字布局：**
   - 文字排列是否整齐
   - 是否有倾斜或变形
   - 表格结构是否完整

4. **干扰因素：**
   - 是否有背景干扰
   - 是否有污渍或折痕
   - 是否有反光或阴影

【输出格式】
```
=== 图片质量评估 ===
整体质量: [优秀/良好/一般/较差]
清晰度: [评分1-10]
可读性: [评分1-10]
OCR成功率预估: [百分比]

=== 具体问题 ===
- [列出发现的问题]
- [提供改善建议]

=== 处理建议 ===
- [是否建议重新拍摄]
- [是否需要图片预处理]
- [OCR识别注意事项]
```

请评估图片质量。
```

---

## 使用说明

### 提示词使用指南

1. **选择合适的提示词：**
   - 首先使用"OTA类型识别提示词"确定订单类型
   - 根据识别结果选择对应的专用提示词
   - 对于图片订单，先使用图片OCR提示词

2. **参数替换：**
   - `{input}`: 替换为实际的订单内容
   - `{current_date}`: 替换为当前日期
   - 其他占位符根据实际情况替换

3. **结果验证：**
   - 检查输出格式是否正确
   - 验证日期时间的合理性
   - 确认信息的完整性

4. **错误处理：**
   - 如果AI输出格式不正确，重新使用提示词
   - 对于识别错误，可以添加更多上下文信息
   - 必要时进行人工校正

### 系统集成说明

本系统已完全集成提示词配置，通过以下文件实现：

- `prompts-config.js`：提示词配置和管理类
- `app.js`：集成了PromptManager的GeminiService
- `index.html`：引用了提示词配置文件

### 在代码中使用

```javascript
// GeminiService已自动初始化PromptManager
const geminiService = new GeminiService();

// 自动OTA类型识别
const detectionResult = await geminiService.detectOTAType(orderContent);

// 处理订单（自动选择合适的提示词）
const result = await geminiService.processOrderWithAI(orderContent, otaType);

// 图片OCR识别
const ocrResult = await geminiService.processImageWithAI(base64ImageData);
```

### 新功能特性

1. **智能OTA类型识别**：系统可自动识别订单属于哪种OTA类型
2. **置信度评估**：提供识别结果的置信度分数
3. **图片OCR集成**：支持直接从图片提取订单信息
4. **多模式处理**：支持文本和图片两种输入方式
5. **错误恢复**：识别失败时自动降级到通用处理模式

### 提示词优化建议

1. **日期处理**：确保日期逻辑清晰，避免过期订单
2. **时间计算**：接机使用到达时间，送机提前3.5小时
3. **酒店名称**：优先使用英文标准名称
4. **唯一性**：确保电话和OTA Reference的唯一性
5. **错误处理**：在other字段中记录异常情况

### 扩展新的OTA类型

要添加新的OTA类型，需要：

1. 在`prompts-config.js`中的`OTA_PROMPTS`对象添加新的提示词模板
2. 在`config.js`中的`OTA_TYPES`注册新的OTA类型
3. 更新`index.html`中的OTA选择器选项
4. 在`OTA_DETECTION_PROMPT`中添加新类型的识别特征
5. 测试新类型的识别和处理效果

### 系统状态监控

系统提供实时状态反馈：
- 蓝色：信息提示（正在处理）
- 绿色：成功完成
- 黄色：警告信息（置信度低等）
- 红色：错误信息（处理失败）

---

*本文档最后更新：2024年12月*